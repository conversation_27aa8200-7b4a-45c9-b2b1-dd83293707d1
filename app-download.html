<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体验未来 - 下载我们的App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 200% 200%;
            animation: gradientShift 8s ease infinite;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        .floating-icon {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .neon-glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
        }

        .download-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
            }

            70% {
                box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
            }
        }

        .feature-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.05);
        }

        .wechat-modal {
            backdrop-filter: blur(15px);
            background: rgba(0, 0, 0, 0.8);
        }

        .screenshot-3d {
            transform: perspective(1000px) rotateY(-15deg);
            transition: transform 0.3s ease;
        }

        .screenshot-3d:hover {
            transform: perspective(1000px) rotateY(0deg);
        }
    </style>
</head>

<body class="bg-black text-white overflow-x-hidden">
    <!-- 微信引导模态框 -->
    <div id="wechatModal" class="fixed inset-0 z-50 wechat-modal hidden items-center justify-center p-4">
        <div
            class="bg-white text-black rounded-3xl p-8 max-w-sm w-full text-center transform scale-95 transition-transform duration-300">
            <div
                class="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <i class="fas fa-external-link-alt text-white text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold mb-3">在浏览器中打开</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">微信限制了应用下载，请点击右上角菜单，选择"在浏览器中打开"继续下载</p>
            <div class="flex items-center justify-center space-x-3 text-sm text-gray-500 bg-gray-50 rounded-xl p-3">
                <i class="fas fa-hand-point-up text-blue-500"></i>
                <span>点击 ⋯ → 在浏览器中打开</span>
            </div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen">
        <!-- 英雄区域 -->
        <div class="hero-gradient min-h-screen relative overflow-hidden">
            <!-- 装饰性浮动元素 -->
            <div class="absolute top-20 left-10 floating-icon opacity-20">
                <i class="fas fa-rocket text-4xl"></i>
            </div>
            <div class="absolute top-40 right-8 floating-icon opacity-20" style="animation-delay: -2s;">
                <i class="fas fa-star text-3xl"></i>
            </div>
            <div class="absolute bottom-40 left-6 floating-icon opacity-20" style="animation-delay: -4s;">
                <i class="fas fa-heart text-3xl"></i>
            </div>

            <div class="container mx-auto px-6 py-16 relative z-10">
                <!-- 应用图标和标题 -->
                <div class="text-center mb-16">
                    <div
                        class="w-32 h-32 mx-auto mb-8 rounded-3xl glass-card neon-glow flex items-center justify-center floating-icon">
                        <i class="fas fa-magic text-6xl text-white"></i>
                    </div>
                    <h1
                        class="text-5xl font-bold mb-4 bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                        企考考
                    </h1>
                    <p class="text-xl text-purple-100 mb-8 leading-relaxed">
                        学练考统统搞定<br>
                    </p>

                    <!-- 快速统计 -->
                    <!-- <div class="flex justify-center space-x-8 mb-12">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-yellow-300">4.9</div>
                            <div class="text-sm text-purple-200">应用评分</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-300">100万+</div>
                            <div class="text-sm text-purple-200">用户下载</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-300">50+</div>
                            <div class="text-sm text-purple-200">国家地区</div>
                        </div>
                    </div> -->
                </div>

                <!-- 下载按钮区域 -->
                <div class="max-w-sm mx-auto space-y-4 mb-16">
                    <button id="androidBtn"
                        class="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-5 px-8 rounded-2xl download-pulse flex items-center justify-between group transition-all duration-300">
                        <div class="flex items-center space-x-4">
                            <i class="fab fa-android text-3xl"></i>
                            <div class="text-left">
                                <div class="text-lg">Android 下载</div>
                                <div class="text-sm opacity-80">Google Play & APK</div>
                            </div>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-2 transition-transform"></i>
                    </button>

                    <!-- <button id="iosBtn"
                        class="w-full bg-gradient-to-r from-gray-700 to-gray-900 hover:from-gray-800 hover:to-black text-white font-bold py-5 px-8 rounded-2xl flex items-center justify-between group transition-all duration-300">
                        <div class="flex items-center space-x-4">
                            <i class="fab fa-apple text-3xl"></i>
                            <div class="text-left">
                                <div class="text-lg">iOS 下载</div>
                                <div class="text-sm opacity-80">App Store</div>
                            </div>
                        </div>
                        <i class="fas fa-arrow-right group-hover:translate-x-2 transition-transform"></i>
                    </button> -->
                </div>
            </div>
        </div>

        <!-- 特色功能区域 -->
        <!-- <div class="bg-gradient-to-b from-black to-gray-900 py-20">
            <div class="container mx-auto px-6">
                <h2
                    class="text-4xl font-bold text-center mb-16 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    为什么选择我们？
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                    <div class="feature-card glass-card rounded-3xl p-8 text-center">
                        <div
                            class="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-bolt text-2xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-4">闪电般快速</h3>
                        <p class="text-gray-300 leading-relaxed">采用最新技术架构，启动速度提升300%，让等待成为过去式</p>
                    </div>

                    <div class="feature-card glass-card rounded-3xl p-8 text-center">
                        <div
                            class="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-brain text-2xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-4">AI智能助手</h3>
                        <p class="text-gray-300 leading-relaxed">内置先进AI引擎，理解你的需求，提供个性化智能建议</p>
                    </div>

                    <div class="feature-card glass-card rounded-3xl p-8 text-center">
                        <div
                            class="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-green-500 to-teal-500 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-shield-alt text-2xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-4">银行级安全</h3>
                        <p class="text-gray-300 leading-relaxed">端到端加密保护，多重安全验证，让你的数据固若金汤</p>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- 应用截图区域 -->
        <div class="bg-gray-900 py-20">
            <div class="container mx-auto px-6">
                <h2 class="text-4xl font-bold text-center mb-16 text-white">
                    沉浸式体验预览
                </h2>

                <div class="flex space-x-6 overflow-x-auto pb-8 max-w-4xl mx-auto">
                    <div
                        class="flex-shrink-0 w-48 h-96 screenshot-3d bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-3xl p-1">
                        <div class="w-full h-full bg-black rounded-3xl flex items-center justify-center">
                            <i class="fas fa-mobile-alt text-6xl text-white"></i>
                        </div>
                    </div>
                    <div
                        class="flex-shrink-0 w-48 h-96 screenshot-3d bg-gradient-to-br from-cyan-500 via-blue-500 to-indigo-500 rounded-3xl p-1">
                        <div class="w-full h-full bg-black rounded-3xl flex items-center justify-center">
                            <i class="fas fa-chart-line text-6xl text-white"></i>
                        </div>
                    </div>
                    <div
                        class="flex-shrink-0 w-48 h-96 screenshot-3d bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500 rounded-3xl p-1">
                        <div class="w-full h-full bg-black rounded-3xl flex items-center justify-center">
                            <i class="fas fa-palette text-6xl text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="bg-black py-16">
            <div class="container mx-auto px-6 text-center">
                <div class="max-w-2xl mx-auto">
                    <div class="flex justify-center space-x-8 mb-8 text-sm text-gray-400">
                        <span><i class="fas fa-download mr-2"></i>文件大小: 28.5 MB</span>
                        <span><i class="fas fa-calendar mr-2"></i>更新: 2024-01-20</span>
                        <span><i class="fas fa-language mr-2"></i>多语言支持</span>
                    </div>

                    <div class="flex justify-center space-x-8 text-gray-500">
                        <a href="#" class="hover:text-purple-400 transition-colors">
                            <i class="fas fa-user-shield mr-2"></i>隐私政策
                        </a>
                        <a href="#" class="hover:text-purple-400 transition-colors">
                            <i class="fas fa-file-contract mr-2"></i>服务条款
                        </a>
                        <a href="#" class="hover:text-purple-400 transition-colors">
                            <i class="fas fa-headset mr-2"></i>客服支持
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检测微信环境
        function isWechat() {
            return /MicroMessenger/i.test(navigator.userAgent);
        }

        // 检测设备类型
        function getDeviceType() {
            const ua = navigator.userAgent;
            if (/iPad|iPhone|iPod/.test(ua)) return 'ios';
            if (/Android/.test(ua)) return 'android';
            return 'unknown';
        }

        // 显示微信引导
        function showWechatModal() {
            const modal = document.getElementById('wechatModal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            setTimeout(() => {
                modal.querySelector('div').classList.remove('scale-95');
                modal.querySelector('div').classList.add('scale-100');
            }, 100);
        }

        // 处理下载
        function handleDownload(type) {
            if (isWechat()) {
                showWechatModal();
                return;
            }

            // 实际下载链接
            const downloadLinks = {
                android: 'https://mp-fb505f95-05ea-4260-82d1-c203cfcde40a.cdn.bspapp.com/cloudstorage/__UNI__0A18F97_20250730100701.apk',
                ios: 'https://apps.apple.com/app/yourapp/id123456789'
            };

            if (downloadLinks[type]) {
                window.open(downloadLinks[type], '_blank');
            }
        }

        // 智能推荐
        function highlightRecommendedPlatform() {
            const deviceType = getDeviceType();
            const androidBtn = document.getElementById('androidBtn');
            const iosBtn = document.getElementById('iosBtn');

            if (deviceType === 'ios') {
                iosBtn.classList.add('ring-4', 'ring-blue-400', 'ring-opacity-50');
                iosBtn.insertAdjacentHTML('afterbegin', '<span class="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">推荐</span>');
            } else if (deviceType === 'android') {
                androidBtn.classList.add('ring-4', 'ring-green-400', 'ring-opacity-50');
                androidBtn.insertAdjacentHTML('afterbegin', '<span class="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">推荐</span>');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            highlightRecommendedPlatform();

            // 绑定下载事件
            document.getElementById('androidBtn').addEventListener('click', () => handleDownload('android'));
            document.getElementById('iosBtn').addEventListener('click', () => handleDownload('ios'));

            // 关闭模态框
            document.getElementById('wechatModal').addEventListener('click', function (e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                    this.classList.remove('flex');
                }
            });

            // 添加滚动视差效果
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelector('.floating-icon');
                if (parallax) {
                    parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });
        });
    </script>
</body>

</html>